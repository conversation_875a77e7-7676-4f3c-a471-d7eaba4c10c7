<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>准备工作 - YOP MCP 开发者指南</title>
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 自定义样式 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-xl font-bold text-gray-900">
                        YOP MCP
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-home text-xl"></i>
                    </a>
                    <a href="https://github.com/yop-platform/yop-mcp-sdk" target="_blank" 
                       class="text-gray-500 hover:text-gray-900">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">准备工作</h1>
            <p class="text-xl text-gray-600">在开始使用 YOP MCP 之前，请确保您已具备以下条件</p>
        </div>

        <!-- 环境要求卡片网格 -->
        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <!-- AI编程环境 -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-robot text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="ml-4 text-xl font-semibold text-gray-900">AI编程环境</h3>
                </div>
                <div class="space-y-4">
                    <p class="text-gray-600">您需要具备以下任一环境：</p>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">支持工具调用的IDE (如Cursor)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">AI编程工具 (如Cline, RooCode)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">通过API调用的LLM (如Claude)</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Python环境 -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fab fa-python text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="ml-4 text-xl font-semibold text-gray-900">Python环境</h3>
                </div>
                <div class="space-y-4">
                    <p class="text-gray-600">确保您的系统已安装：</p>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">Python 3.8 或更高版本</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">包管理工具 uv (推荐) 或 pip</span>
                        </li>
                    </ul>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <code class="text-sm text-gray-700 whitespace-pre">
# 检查Python版本
python --version

# 安装uv (推荐)
curl -LsSf https://astral.sh/uv/install.sh | sh</code>
                    </div>
                </div>
            </div>

            <!-- 易宝支付商户信息 -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-id-card text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="ml-4 text-xl font-semibold text-gray-900">商户信息</h3>
                </div>
                <div class="space-y-4">
                    <p class="text-gray-600">实际API对接时需要：</p>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">易宝支付商户编号 (merchantNo)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">应用密钥对 (将由YOP MCP协助生成)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-1 mr-2"></i>
                            <span class="text-gray-700">测试时可使用沙箱环境数据</span>
                        </li>
                    </ul>
                    <div class="mt-4 bg-blue-50 border-l-4 border-blue-500 p-4">
                        <p class="text-blue-700 text-sm">
                            提示：沙箱环境提供模拟数据，方便您进行开发测试。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下一步操作 -->
        <div class="mt-12 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">准备就绪？让我们开始吧！</h2>
            <div class="flex justify-center space-x-4">
                <a href="quick-start.html" 
                   class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-rocket mr-2"></i>
                    开始配置
                </a>
                <a href="../index.html#features" 
                   class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回首页
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 py-12 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <span class="text-xl font-bold text-white block mb-4">YOP MCP</span>
                    <p class="text-gray-400">
                        YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集，
                        让您通过AI助手轻松完成支付集成。
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">资源</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">开发者文档</a></li>
                        <li><a href="#" class="hover:text-white">API 文档</a></li>
                        <li><a href="#" class="hover:text-white">示例代码</a></li>
                        <li><a href="#" class="hover:text-white">常见问题</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">技术支持</a></li>
                        <li><a href="#" class="hover:text-white">商务合作</a></li>
                        <li><a href="#" class="hover:text-white">加入我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 易宝支付. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
