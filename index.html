<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOP MCP：一行提示词，AI 驱动您的易宝支付极速集成！</title>
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 自定义样式 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0EA5E9 0%, #2563EB 100%);
        }
        
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="text-xl font-bold text-gray-900">
                        YOP MCP
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#features" class="text-gray-600 hover:text-gray-900">特性</a>
                    <a href="#quick-start" class="text-gray-600 hover:text-gray-900">快速开始</a>
                    <a href="https://github.com/yop-platform/yop-mcp-sdk" target="_blank" 
                       class="text-gray-500 hover:text-gray-900">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg hero-pattern pt-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="lg:flex lg:items-center lg:justify-between">
                <div class="lg:w-1/2">
                    <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        YOP MCP 来了！<br>
                        AI 赋能，一行提示词接入易宝支付。
                    </h1>
                    <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                        专为开发者打造的全新集成体验，将数天的对接工作缩短至小时级，甚至分钟级。
                        告别繁琐，拥抱智能，让AI成为您的易宝支付集成伙伴！
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#quick-start" 
                           class="inline-flex items-center justify-center px-8 py-3 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors duration-200">
                            <i class="fas fa-rocket mr-2"></i>
                            立即开始配置 YOP MCP
                        </a>
                        <a href="#demo-video" 
                           class="inline-flex items-center justify-center px-8 py-3 text-base font-medium rounded-lg text-white border-2 border-white hover:bg-white hover:text-blue-600 transition-colors duration-200">
                            <i class="fas fa-play-circle mr-2"></i>
                            观看演示视频
                        </a>
                    </div>
                </div>
                <div class="lg:w-1/2 mt-10 lg:mt-0">
                    <div class="relative rounded-lg shadow-xl bg-white p-6">
                        <div class="flex items-center mb-4">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700 bg-gray-100 rounded-lg p-3">
                                        请帮我实现易宝支付的订单支付功能
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-robot text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm text-gray-700 bg-blue-50 rounded-lg p-3">
                                        <p class="mb-2">正在通过YOP MCP分析最佳实现方案...</p>
                                        <code class="block bg-gray-800 text-green-400 p-2 rounded text-xs">
                                            // 生成的Python代码示例
                                            from yop import YopClient
                                            
                                            client = YopClient("your_app_key")
                                            response = client.pay.create({
                                                "order_id": "12345",
                                                "amount": 100.00,
                                                "currency": "CNY"
                                            })
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white" id="features">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
                释放 AI 的力量，革命化您的支付集成
            </h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-bolt text-blue-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">极速高效</h3>
                    <p class="text-gray-600">
                        从需求分析到API集成，AI全程加速，将传统模式下数天的工作缩短至小时级。
                    </p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-magic text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">简单易用</h3>
                    <p class="text-gray-600">
                        告别繁复的API文档，用自然语言对话即可驱动复杂的API对接任务。
                    </p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-brain text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">智能强大</h3>
                    <p class="text-gray-600">
                        AI深度理解您的集成意图，结合实时更新的官方文档提供精准可靠的方案。
                    </p>
                </div>
                
                <!-- Feature 4 -->
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-shield-alt text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">安全可控</h3>
                    <p class="text-gray-600">
                        核心工具在本地执行，敏感信息的生成与存储均在您的掌控之中。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start Section -->
    <section class="py-20 bg-gray-50" id="quick-start">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
                只需两步，激活您的 AI 集成超能力！
            </h2>
            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Step 1 -->
                <div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                1
                            </div>
                            <h3 class="ml-3 text-xl font-semibold">安装 YOP MCP 命令行工具</h3>
                        </div>
                        <div class="mb-4">
                            <div class="bg-gray-800 rounded-lg p-4">
                                <code class="text-green-400 text-sm">
                                    # 创建虚拟环境
                                    python -m venv yop_mcp_env
                                    
                                    # 激活环境
                                    source yop_mcp_env/bin/activate  # Linux/macOS
                                    
                                    # 安装 yop-mcp
                                    uv pip install yop-mcp
                                </code>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            确保您已安装 Python 3.8+ 和包管理工具 uv 或 pip
                        </p>
                    </div>
                </div>
                
                <!-- Step 2 -->
                <div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                2
                            </div>
                            <h3 class="ml-3 text-xl font-semibold">配置 AI 工具</h3>
                        </div>
                        <div class="mb-4">
                            <div class="bg-gray-800 rounded-lg p-4">
                                <code class="text-green-400 text-sm">
                                    {
                                      "mcpServers": {
                                        "mcpadvisor": {
                                          "command": "uv",
                                          "args": [
                                            "yop-mcp"
                                          ]
                                        }
                                      }
                                    }
                                </code>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            在您的IDE或AI编程工具中添加上述MCP配置
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 开始使用按钮 -->
            <div class="mt-12 text-center">
                <a href="pages/prerequisites.html" 
                   class="inline-flex items-center justify-center px-8 py-3 text-lg font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-book mr-2"></i>
                    查看完整文档
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <span class="text-xl font-bold text-white block mb-4">YOP MCP</span>
                    <p class="text-gray-400">
                        YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集，
                        让您通过AI助手轻松完成支付集成。
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">资源</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">开发者文档</a></li>
                        <li><a href="#" class="hover:text-white">API 文档</a></li>
                        <li><a href="#" class="hover:text-white">示例代码</a></li>
                        <li><a href="#" class="hover:text-white">常见问题</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">技术支持</a></li>
                        <li><a href="#" class="hover:text-white">商务合作</a></li>
                        <li><a href="#" class="hover:text-white">加入我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 易宝支付. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
